# This is the CMakeCache file.
# For build in directory: /home/<USER>/Desktop/RoadLib-master/build
# It was generated by CMake: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a library.
BLAS_atlas_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libatlas.so

//Path to a library.
BLAS_f77blas_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libf77blas.so

//Path to a library.
BLAS_goto2_LIBRARY:FILEPATH=BLAS_goto2_LIBRARY-NOTFOUND

//Path to a library.
BLAS_openblas_LIBRARY:FILEPATH=BLAS_openblas_LIBRARY-NOTFOUND

//The threading library used by boost-thread
BOOST_THREAD_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpthread.so

//Boost atomic library (debug)
Boost_ATOMIC_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_atomic.so

//Boost atomic library (release)
Boost_ATOMIC_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_atomic.so

//Boost chrono library (debug)
Boost_CHRONO_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_chrono.so

//Boost chrono library (release)
Boost_CHRONO_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_chrono.so

//Boost date_time library (debug)
Boost_DATE_TIME_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_date_time.so

//Boost date_time library (release)
Boost_DATE_TIME_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_date_time.so

//The directory containing a CMake configuration file for Boost.
Boost_DIR:PATH=Boost_DIR-NOTFOUND

//Boost filesystem library (debug)
Boost_FILESYSTEM_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_filesystem.so

//Boost filesystem library (release)
Boost_FILESYSTEM_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_filesystem.so

//Path to a file.
Boost_INCLUDE_DIR:PATH=/usr/include

//Boost iostreams library (debug)
Boost_IOSTREAMS_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_iostreams.so

//Boost iostreams library (release)
Boost_IOSTREAMS_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_iostreams.so

//Boost library directory DEBUG
Boost_LIBRARY_DIR_DEBUG:PATH=/usr/lib/x86_64-linux-gnu

//Boost library directory RELEASE
Boost_LIBRARY_DIR_RELEASE:PATH=/usr/lib/x86_64-linux-gnu

//Boost program_options library (debug)
Boost_PROGRAM_OPTIONS_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_program_options.so

//Boost program_options library (release)
Boost_PROGRAM_OPTIONS_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_program_options.so

//Boost regex library (debug)
Boost_REGEX_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_regex.so

//Boost regex library (release)
Boost_REGEX_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_regex.so

//Boost serialization library (debug)
Boost_SERIALIZATION_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_serialization.so

//Boost serialization library (release)
Boost_SERIALIZATION_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_serialization.so

//Boost system library (debug)
Boost_SYSTEM_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_system.so

//Boost system library (release)
Boost_SYSTEM_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_system.so

//Boost thread library (debug)
Boost_THREAD_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_thread.so

//Boost thread library (release)
Boost_THREAD_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libboost_thread.so

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: None(CMAKE_CXX_FLAGS or
// CMAKE_C_FLAGS used) Debug Release RelWithDebInfo MinSizeRel.
CMAKE_BUILD_TYPE:STRING=

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-8

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-8

//Flags used by the compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the compiler during debug builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the compiler during release builds for minimum
// size.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the compiler during release builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the compiler during release builds with debug info.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/usr/bin/cc

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-8

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-8

//Flags used by the compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the compiler during debug builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the compiler during release builds for minimum
// size.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the compiler during release builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the compiler during release builds with debug info.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Flags used by the linker.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during debug builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during release minsize builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during release builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during Release with Debug Info builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=OFF

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/make

//Flags used by the linker during the creation of modules.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during debug builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during release minsize builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during release builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during Release with Debug Info builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=roadlib

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Flags used by the linker during the creation of dll's.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during debug builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during release minsize builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during release builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during Release with Debug Info builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during debug builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during release minsize builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during release builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during Release with Debug Info builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//The directory containing a CMake configuration file for CXSparse.
CXSparse_DIR:PATH=CXSparse_DIR-NOTFOUND

//Path to a file.
CXSparse_INCLUDE_DIR:PATH=/usr/include/suitesparse

//Path to a library.
CXSparse_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcxsparse.so

//The directory containing a CMake configuration file for Ceres.
Ceres_DIR:PATH=/usr/local/lib/cmake/Ceres

//Path to a file.
DAVIDSDK_INCLUDE_DIR:PATH=DAVIDSDK_INCLUDE_DIR-NOTFOUND

//Path to a library.
DAVIDSDK_LIBRARY:FILEPATH=DAVIDSDK_LIBRARY-NOTFOUND

//DepthSense SDK directory
DSSDK_DIR:PATH=DSSDK_DIR-NOTFOUND

//Path to a library.
DSSDK_LIBRARY_DepthSense:FILEPATH=DSSDK_LIBRARY_DepthSense-NOTFOUND

//Path to a library.
DSSDK_LIBRARY_DepthSensePlugins:FILEPATH=DSSDK_LIBRARY_DepthSensePlugins-NOTFOUND

//Path to a library.
DSSDK_LIBRARY_turbojpeg:FILEPATH=DSSDK_LIBRARY_turbojpeg-NOTFOUND

//Path to a file.
EIGEN_INCLUDE_DIRS:PATH=/usr/include/eigen3

//Path to a file.
ENSENSO_INCLUDE_DIR:PATH=ENSENSO_INCLUDE_DIR-NOTFOUND

//Path to a library.
ENSENSO_LIBRARY:FILEPATH=ENSENSO_LIBRARY-NOTFOUND

//The directory containing a CMake configuration file for Eigen3.
Eigen3_DIR:PATH=/usr/lib/cmake/eigen3

//Path to a file.
FLANN_INCLUDE_DIRS:PATH=/usr/include

//Path to a library.
FLANN_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libflann_cpp_s.a

//Path to a library.
FLANN_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libflann_cpp_s.a

//Path to a file.
GLOG_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
GLOG_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libglog.so

//Path to a library.
LAPACK_Accelerate_LIBRARY:FILEPATH=LAPACK_Accelerate_LIBRARY-NOTFOUND

//Path to a library.
LAPACK_goto2_LIBRARY:FILEPATH=LAPACK_goto2_LIBRARY-NOTFOUND

//Path to a library.
LAPACK_lapack_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/liblapack.so

//Path to a library.
LAPACK_openblas_LIBRARY:FILEPATH=LAPACK_openblas_LIBRARY-NOTFOUND

//Path to a library.
LAPACK_vecLib_LIBRARY:FILEPATH=LAPACK_vecLib_LIBRARY-NOTFOUND

//Path to a file.
LIBUSB_1_INCLUDE_DIR:PATH=LIBUSB_1_INCLUDE_DIR-NOTFOUND

//Path to a library.
LIBUSB_1_LIBRARY:FILEPATH=LIBUSB_1_LIBRARY-NOTFOUND

//METIS include directory
METIS_INCLUDE_DIR:PATH=METIS_INCLUDE_DIR-NOTFOUND

//METIS debug library
METIS_LIBRARY_DEBUG:FILEPATH=METIS_LIBRARY_DEBUG-NOTFOUND

//METIS release library
METIS_LIBRARY_RELEASE:FILEPATH=METIS_LIBRARY_RELEASE-NOTFOUND

//Path to a file.
OPENGL_EGL_INCLUDE_DIR:PATH=/usr/include

//Path to a file.
OPENGL_GLX_INCLUDE_DIR:PATH=/usr/include

//Path to a file.
OPENGL_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
OPENGL_egl_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libEGL.so

//Path to a library.
OPENGL_gl_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libGL.so

//Path to a library.
OPENGL_glu_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libGLU.so

//Path to a library.
OPENGL_glx_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libGLX.so

//Path to a library.
OPENGL_opengl_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libOpenGL.so

//Path to a file.
OPENGL_xmesa_INCLUDE_DIR:PATH=OPENGL_xmesa_INCLUDE_DIR-NOTFOUND

//Path to a file.
OPENNI2_INCLUDE_DIRS:PATH=/usr/include/openni2

//Path to a library.
OPENNI2_LIBRARY:FILEPATH=/usr/lib/libOpenNI2.so

//Path to a file.
OPENNI_INCLUDE_DIRS:PATH=/usr/include/ni

//Path to a library.
OPENNI_LIBRARY:FILEPATH=/usr/lib/libOpenNI.so

//The directory containing a CMake configuration file for OpenCV.
OpenCV_DIR:PATH=/usr/share/OpenCV

//path to 2d headers
PCL_2D_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to apps headers
PCL_APPS_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_apps library
PCL_APPS_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_apps.so

//path to pcl_apps library debug
PCL_APPS_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_apps.so

//path to common headers
PCL_COMMON_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_common library
PCL_COMMON_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_common.so

//path to pcl_common library debug
PCL_COMMON_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_common.so

//The directory containing a CMake configuration file for PCL.
PCL_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/pcl

//path to features headers
PCL_FEATURES_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_features library
PCL_FEATURES_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_features.so

//path to pcl_features library debug
PCL_FEATURES_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_features.so

//path to filters headers
PCL_FILTERS_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_filters library
PCL_FILTERS_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_filters.so

//path to pcl_filters library debug
PCL_FILTERS_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_filters.so

//path to geometry headers
PCL_GEOMETRY_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to in_hand_scanner headers
PCL_IN_HAND_SCANNER_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to io headers
PCL_IO_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_io library
PCL_IO_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_io.so

//path to pcl_io library debug
PCL_IO_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_io.so

//path to kdtree headers
PCL_KDTREE_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_kdtree library
PCL_KDTREE_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_kdtree.so

//path to pcl_kdtree library debug
PCL_KDTREE_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_kdtree.so

//path to keypoints headers
PCL_KEYPOINTS_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_keypoints library
PCL_KEYPOINTS_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_keypoints.so

//path to pcl_keypoints library debug
PCL_KEYPOINTS_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_keypoints.so

//path to ml headers
PCL_ML_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_ml library
PCL_ML_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_ml.so

//path to pcl_ml library debug
PCL_ML_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_ml.so

//path to modeler headers
PCL_MODELER_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to octree headers
PCL_OCTREE_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_octree library
PCL_OCTREE_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_octree.so

//path to pcl_octree library debug
PCL_OCTREE_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_octree.so

//path to outofcore headers
PCL_OUTOFCORE_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_outofcore library
PCL_OUTOFCORE_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_outofcore.so

//path to pcl_outofcore library debug
PCL_OUTOFCORE_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_outofcore.so

//path to people headers
PCL_PEOPLE_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_people library
PCL_PEOPLE_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_people.so

//path to pcl_people library debug
PCL_PEOPLE_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_people.so

//path to point_cloud_editor headers
PCL_POINT_CLOUD_EDITOR_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to recognition headers
PCL_RECOGNITION_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_recognition library
PCL_RECOGNITION_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_recognition.so

//path to pcl_recognition library debug
PCL_RECOGNITION_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_recognition.so

//path to registration headers
PCL_REGISTRATION_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_registration library
PCL_REGISTRATION_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_registration.so

//path to pcl_registration library debug
PCL_REGISTRATION_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_registration.so

//path to sample_consensus headers
PCL_SAMPLE_CONSENSUS_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_sample_consensus library
PCL_SAMPLE_CONSENSUS_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so

//path to pcl_sample_consensus library debug
PCL_SAMPLE_CONSENSUS_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so

//path to search headers
PCL_SEARCH_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_search library
PCL_SEARCH_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_search.so

//path to pcl_search library debug
PCL_SEARCH_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_search.so

//path to segmentation headers
PCL_SEGMENTATION_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_segmentation library
PCL_SEGMENTATION_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_segmentation.so

//path to pcl_segmentation library debug
PCL_SEGMENTATION_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_segmentation.so

//path to stereo headers
PCL_STEREO_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_stereo library
PCL_STEREO_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_stereo.so

//path to pcl_stereo library debug
PCL_STEREO_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_stereo.so

//path to surface headers
PCL_SURFACE_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_surface library
PCL_SURFACE_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_surface.so

//path to pcl_surface library debug
PCL_SURFACE_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_surface.so

//path to tracking headers
PCL_TRACKING_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_tracking library
PCL_TRACKING_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_tracking.so

//path to pcl_tracking library debug
PCL_TRACKING_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_tracking.so

//path to visualization headers
PCL_VISUALIZATION_INCLUDE_DIR:PATH=/usr/include/pcl-1.8

//path to pcl_visualization library
PCL_VISUALIZATION_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_visualization.so

//path to pcl_visualization library debug
PCL_VISUALIZATION_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libpcl_visualization.so

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/bin/pkg-config

//Path to a file.
QHULL_INCLUDE_DIRS:PATH=/usr/include

//Path to a library.
QHULL_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libqhull.so

//Path to a library.
QHULL_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libqhull.so

//The directory containing a CMake configuration file for Qt5Core.
Qt5Core_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/Qt5Core

//The directory containing a CMake configuration file for Qt5Gui.
Qt5Gui_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/Qt5Gui

//The directory containing a CMake configuration file for Qt5Network.
Qt5Network_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/Qt5Network

//The directory containing a CMake configuration file for Qt5WebKit.
Qt5WebKit_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/Qt5WebKit

//RealSense SDK directory
RSSDK_DIR:PATH=RSSDK_DIR-NOTFOUND

//Path to a library.
RSSDK_LIBRARY:FILEPATH=RSSDK_LIBRARY-NOTFOUND

//Path to a library.
RSSDK_LIBRARY_DEBUG:FILEPATH=RSSDK_LIBRARY_DEBUG-NOTFOUND

//Path to a file.
SuiteSparse_AMD_INCLUDE_DIR:PATH=/usr/include/suitesparse

//Path to a library.
SuiteSparse_AMD_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libamd.so

//Path to a file.
SuiteSparse_CAMD_INCLUDE_DIR:PATH=/usr/include/suitesparse

//Path to a library.
SuiteSparse_CAMD_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcamd.so

//Path to a file.
SuiteSparse_CCOLAMD_INCLUDE_DIR:PATH=/usr/include/suitesparse

//Path to a library.
SuiteSparse_CCOLAMD_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libccolamd.so

//Path to a file.
SuiteSparse_CHOLMOD_INCLUDE_DIR:PATH=/usr/include/suitesparse

//Path to a library.
SuiteSparse_CHOLMOD_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcholmod.so

//Path to a file.
SuiteSparse_COLAMD_INCLUDE_DIR:PATH=/usr/include/suitesparse

//Path to a library.
SuiteSparse_COLAMD_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcolamd.so

//Path to a file.
SuiteSparse_Config_INCLUDE_DIR:PATH=/usr/include/suitesparse

//Path to a library.
SuiteSparse_Config_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libsuitesparseconfig.so

//The directory containing a CMake configuration file for SuiteSparse.
SuiteSparse_DIR:PATH=SuiteSparse_DIR-NOTFOUND

//Path to a file.
SuiteSparse_SPQR_INCLUDE_DIR:PATH=/usr/include/suitesparse

//Path to a library.
SuiteSparse_SPQR_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libspqr.so

//The directory containing a CMake configuration file for TBB.
TBB_DIR:PATH=TBB_DIR-NOTFOUND

//The directory containing VTKConfig.cmake
VTK_DIR:PATH=/usr/lib/cmake/vtk-6.3

//The directory containing a CMake configuration file for gflags.
gflags_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/gflags

//The directory containing a CMake configuration file for glfw3.
glfw3_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/glfw3

//Value Computed by CMake
roadlib_BINARY_DIR:STATIC=/home/<USER>/Desktop/RoadLib-master/build

//Value Computed by CMake
roadlib_SOURCE_DIR:STATIC=/home/<USER>/Desktop/RoadLib-master


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: BLAS_atlas_LIBRARY
BLAS_atlas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_f77blas_LIBRARY
BLAS_f77blas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_f77blas_atlas_WORKS
BLAS_f77blas_atlas_WORKS-ADVANCED:INTERNAL=1
//Have function dgemm_
BLAS_f77blas_atlas_WORKS:INTERNAL=1
//ADVANCED property for variable: BLAS_goto2_LIBRARY
BLAS_goto2_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_openblas_LIBRARY
BLAS_openblas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_ATOMIC_LIBRARY_DEBUG
Boost_ATOMIC_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_ATOMIC_LIBRARY_RELEASE
Boost_ATOMIC_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_CHRONO_LIBRARY_DEBUG
Boost_CHRONO_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_CHRONO_LIBRARY_RELEASE
Boost_CHRONO_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_DATE_TIME_LIBRARY_DEBUG
Boost_DATE_TIME_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_DATE_TIME_LIBRARY_RELEASE
Boost_DATE_TIME_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_DIR
Boost_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_FILESYSTEM_LIBRARY_DEBUG
Boost_FILESYSTEM_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_FILESYSTEM_LIBRARY_RELEASE
Boost_FILESYSTEM_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_INCLUDE_DIR
Boost_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_IOSTREAMS_LIBRARY_DEBUG
Boost_IOSTREAMS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_IOSTREAMS_LIBRARY_RELEASE
Boost_IOSTREAMS_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_LIBRARY_DIR_DEBUG
Boost_LIBRARY_DIR_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_LIBRARY_DIR_RELEASE
Boost_LIBRARY_DIR_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_PROGRAM_OPTIONS_LIBRARY_DEBUG
Boost_PROGRAM_OPTIONS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_PROGRAM_OPTIONS_LIBRARY_RELEASE
Boost_PROGRAM_OPTIONS_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_REGEX_LIBRARY_DEBUG
Boost_REGEX_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_REGEX_LIBRARY_RELEASE
Boost_REGEX_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_SERIALIZATION_LIBRARY_DEBUG
Boost_SERIALIZATION_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_SERIALIZATION_LIBRARY_RELEASE
Boost_SERIALIZATION_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_SYSTEM_LIBRARY_DEBUG
Boost_SYSTEM_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_SYSTEM_LIBRARY_RELEASE
Boost_SYSTEM_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_THREAD_LIBRARY_DEBUG
Boost_THREAD_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_THREAD_LIBRARY_RELEASE
Boost_THREAD_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/Desktop/RoadLib-master/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=10
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=2
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/usr/bin/cmake-gui
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Have symbol pthread_create
CMAKE_HAVE_LIBC_CREATE:INTERNAL=
//Have library pthreads
CMAKE_HAVE_PTHREADS_CREATE:INTERNAL=
//Have library pthread
CMAKE_HAVE_PTHREAD_CREATE:INTERNAL=1
//Have include pthread.h
CMAKE_HAVE_PTHREAD_H:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/Desktop/RoadLib-master
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake-3.10
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CXSparse_INCLUDE_DIR
CXSparse_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CXSparse_LIBRARY
CXSparse_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: DSSDK_LIBRARY_DepthSense
DSSDK_LIBRARY_DepthSense-ADVANCED:INTERNAL=1
//ADVANCED property for variable: DSSDK_LIBRARY_DepthSensePlugins
DSSDK_LIBRARY_DepthSensePlugins-ADVANCED:INTERNAL=1
//ADVANCED property for variable: DSSDK_LIBRARY_turbojpeg
DSSDK_LIBRARY_turbojpeg-ADVANCED:INTERNAL=1
//Details about finding CXSparse
FIND_PACKAGE_MESSAGE_DETAILS_CXSparse:INTERNAL=[/usr/include/suitesparse][/usr/lib/x86_64-linux-gnu/libcxsparse.so][v3.1.9(3.1.9)]
//Details about finding Flann
FIND_PACKAGE_MESSAGE_DETAILS_Flann:INTERNAL=[/usr/lib/x86_64-linux-gnu/libflann_cpp_s.a][/usr/include][v()]
//Details about finding OpenCV
FIND_PACKAGE_MESSAGE_DETAILS_OpenCV:INTERNAL=[/usr][v3.2.0()]
//Details about finding OpenGL
FIND_PACKAGE_MESSAGE_DETAILS_OpenGL:INTERNAL=[/usr/lib/x86_64-linux-gnu/libOpenGL.so][/usr/lib/x86_64-linux-gnu/libGLX.so][/usr/include][c ][v()]
//Details about finding OpenNI2
FIND_PACKAGE_MESSAGE_DETAILS_OpenNI2:INTERNAL=[/usr/lib/libOpenNI2.so][/usr/include/openni2][v()]
//Details about finding PCL
FIND_PACKAGE_MESSAGE_DETAILS_PCL:INTERNAL=[/usr/lib/x86_64-linux-gnu/libboost_system.so;/usr/lib/x86_64-linux-gnu/libboost_filesystem.so;/usr/lib/x86_64-linux-gnu/libboost_thread.so;/usr/lib/x86_64-linux-gnu/libboost_date_time.so;/usr/lib/x86_64-linux-gnu/libboost_iostreams.so;/usr/lib/x86_64-linux-gnu/libboost_serialization.so;/usr/lib/x86_64-linux-gnu/libboost_chrono.so;/usr/lib/x86_64-linux-gnu/libboost_atomic.so;/usr/lib/x86_64-linux-gnu/libboost_regex.so;/usr/lib/x86_64-linux-gnu/libpthread.so;optimized;/usr/lib/x86_64-linux-gnu/libpcl_common.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_common.so;optimized;/usr/lib/x86_64-linux-gnu/libpcl_octree.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_octree.so;/usr/lib/libOpenNI.so;/usr/lib/libOpenNI2.so;vtkChartsCore;vtkCommonColor;vtkCommonDataModel;vtkCommonMath;vtkCommonCore;vtksys;vtkCommonMisc;vtkCommonSystem;vtkCommonTransforms;vtkInfovisCore;vtkFiltersExtraction;vtkCommonExecutionModel;vtkFiltersCore;vtkFiltersGeneral;vtkCommonComputationalGeometry;vtkFiltersStatistics;vtkImagingFourier;vtkImagingCore;vtkalglib;vtkRenderingContext2D;vtkRenderingCore;vtkFiltersGeometry;vtkFiltersSources;vtkRenderingFreeType;/usr/lib/x86_64-linux-gnu/libfreetype.so;/usr/lib/x86_64-linux-gnu/libz.so;vtkftgl;vtkDICOMParser;vtkDomainsChemistry;vtkIOXML;vtkIOGeometry;vtkIOCore;vtkIOXMLParser;/usr/lib/x86_64-linux-gnu/libexpat.so;vtkFiltersAMR;vtkParallelCore;vtkIOLegacy;vtkFiltersFlowPaths;vtkFiltersGeneric;vtkFiltersHybrid;vtkImagingSources;vtkFiltersHyperTree;vtkFiltersImaging;vtkImagingGeneral;vtkFiltersModeling;vtkFiltersParallel;vtkFiltersParallelFlowPaths;vtkParallelMPI;vtkFiltersParallelGeometry;vtkFiltersParallelImaging;vtkFiltersParallelMPI;vtkFiltersParallelStatistics;vtkFiltersProgrammable;vtkFiltersPython;/usr/lib/x86_64-linux-gnu/libpython2.7.so;vtkWrappingPythonCore;vtkWrappingTools;vtkFiltersReebGraph;vtkFiltersSMP;vtkFiltersSelection;vtkFiltersTexture;vtkFiltersVerdict;verdict;vtkGUISupportQt;vtkInteractionStyle;vtkRenderingOpenGL;vtkImagingHybrid;vtkIOImage;vtkmetaio;/usr/lib/x86_64-linux-gnu/libjpeg.so;/usr/lib/x86_64-linux-gnu/libpng.so;/usr/lib/x86_64-linux-gnu/libtiff.so;vtkGUISupportQtOpenGL;vtkGUISupportQtSQL;vtkIOSQL;sqlite3;vtkGUISupportQtWebkit;vtkViewsQt;vtkViewsInfovis;vtkInfovisLayout;vtkInfovisBoostGraphAlgorithms;vtkRenderingLabel;vtkViewsCore;vtkInteractionWidgets;vtkRenderingAnnotation;vtkImagingColor;vtkRenderingVolume;vtkGeovisCore;/usr/lib/x86_64-linux-gnu/libproj.so;vtkIOAMR;/usr/lib/x86_64-linux-gnu/hdf5/openmpi/libhdf5.so;/usr/lib/x86_64-linux-gnu/libsz.so;/usr/lib/x86_64-linux-gnu/libdl.so;/usr/lib/x86_64-linux-gnu/libm.so;/usr/lib/x86_64-linux-gnu/openmpi/lib/libmpi.so;vtkIOEnSight;vtkIOExodus;vtkexoIIc;/usr/lib/x86_64-linux-gnu/libnetcdf_c++.so;/usr/lib/x86_64-linux-gnu/libnetcdf.so;vtkIOExport;vtkRenderingGL2PS;vtkRenderingContextOpenGL;/usr/lib/x86_64-linux-gnu/libgl2ps.so;vtkIOFFMPEG;vtkIOMovie;/usr/lib/x86_64-linux-gnu/libtheoraenc.so;/usr/lib/x86_64-linux-gnu/libtheoradec.so;/usr/lib/x86_64-linux-gnu/libogg.so;vtkIOGDAL;vtkIOGeoJSON;vtkIOImport;vtkIOInfovis;/usr/lib/x86_64-linux-gnu/libxml2.so;vtkIOLSDyna;vtkIOMINC;vtkIOMPIImage;vtkIOMPIParallel;vtkIOParallel;vtkIONetCDF;/usr/lib/x86_64-linux-gnu/libjsoncpp.so;vtkIOMySQL;vtkIOODBC;vtkIOPLY;vtkIOParallelExodus;vtkIOParallelLSDyna;vtkIOParallelNetCDF;vtkIOParallelXML;vtkIOPostgreSQL;vtkIOVPIC;VPIC;vtkIOVideo;vtkIOXdmf2;vtkxdmf2;vtkImagingMath;vtkImagingMorphological;vtkImagingStatistics;vtkImagingStencil;vtkInteractionImage;vtkLocalExample;vtkParallelMPI4Py;vtkPythonInterpreter;vtkRenderingExternal;vtkRenderingFreeTypeFontConfig;vtkRenderingImage;vtkRenderingLIC;vtkRenderingLOD;vtkRenderingMatplotlib;vtkRenderingParallel;vtkRenderingParallelLIC;vtkRenderingQt;vtkRenderingVolumeAMR;vtkRenderingVolumeOpenGL;vtkTestingGenericBridge;vtkTestingIOSQL;vtkTestingRendering;vtkViewsContext2D;vtkViewsGeovis;vtkWrappingJava;optimized;/usr/lib/x86_64-linux-gnu/libpcl_io.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_io.so;optimized;/usr/lib/x86_64-linux-gnu/libflann_cpp_s.a;debug;/usr/lib/x86_64-linux-gnu/libflann_cpp_s.a;optimized;/usr/lib/x86_64-linux-gnu/libpcl_kdtree.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_kdtree.so;optimized;/usr/lib/x86_64-linux-gnu/libpcl_search.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_search.so;optimized;/usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so;optimized;/usr/lib/x86_64-linux-gnu/libpcl_filters.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_filters.so;optimized;/usr/lib/x86_64-linux-gnu/libpcl_features.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_features.so;optimized;/usr/lib/x86_64-linux-gnu/libpcl_ml.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_ml.so;optimized;/usr/lib/x86_64-linux-gnu/libpcl_segmentation.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_segmentation.so;optimized;/usr/lib/x86_64-linux-gnu/libpcl_visualization.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_visualization.so;optimized;/usr/lib/x86_64-linux-gnu/libqhull.so;debug;/usr/lib/x86_64-linux-gnu/libqhull.so;optimized;/usr/lib/x86_64-linux-gnu/libpcl_surface.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_surface.so;optimized;/usr/lib/x86_64-linux-gnu/libpcl_registration.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_registration.so;optimized;/usr/lib/x86_64-linux-gnu/libpcl_keypoints.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_keypoints.so;optimized;/usr/lib/x86_64-linux-gnu/libpcl_tracking.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_tracking.so;optimized;/usr/lib/x86_64-linux-gnu/libpcl_recognition.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_recognition.so;optimized;/usr/lib/x86_64-linux-gnu/libpcl_stereo.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_stereo.so;optimized;/usr/lib/x86_64-linux-gnu/libpcl_apps.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_apps.so;optimized;/usr/lib/x86_64-linux-gnu/libpcl_outofcore.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_outofcore.so;optimized;/usr/lib/x86_64-linux-gnu/libpcl_people.so;debug;/usr/lib/x86_64-linux-gnu/libpcl_people.so;/usr/lib/x86_64-linux-gnu/libboost_system.so;/usr/lib/x86_64-linux-gnu/libboost_filesystem.so;/usr/lib/x86_64-linux-gnu/libboost_thread.so;/usr/lib/x86_64-linux-gnu/libboost_date_time.so;/usr/lib/x86_64-linux-gnu/libboost_iostreams.so;/usr/lib/x86_64-linux-gnu/libboost_serialization.so;/usr/lib/x86_64-linux-gnu/libboost_chrono.so;/usr/lib/x86_64-linux-gnu/libboost_atomic.so;/usr/lib/x86_64-linux-gnu/libboost_regex.so;/usr/lib/x86_64-linux-gnu/libpthread.so;optimized;/usr/lib/x86_64-linux-gnu/libqhull.so;debug;/usr/lib/x86_64-linux-gnu/libqhull.so;/usr/lib/libOpenNI.so;/usr/lib/libOpenNI2.so;optimized;/usr/lib/x86_64-linux-gnu/libflann_cpp_s.a;debug;/usr/lib/x86_64-linux-gnu/libflann_cpp_s.a;vtkChartsCore;vtkCommonColor;vtkCommonDataModel;vtkCommonMath;vtkCommonCore;vtksys;vtkCommonMisc;vtkCommonSystem;vtkCommonTransforms;vtkInfovisCore;vtkFiltersExtraction;vtkCommonExecutionModel;vtkFiltersCore;vtkFiltersGeneral;vtkCommonComputationalGeometry;vtkFiltersStatistics;vtkImagingFourier;vtkImagingCore;vtkalglib;vtkRenderingContext2D;vtkRenderingCore;vtkFiltersGeometry;vtkFiltersSources;vtkRenderingFreeType;/usr/lib/x86_64-linux-gnu/libfreetype.so;/usr/lib/x86_64-linux-gnu/libz.so;vtkftgl;vtkDICOMParser;vtkDomainsChemistry;vtkIOXML;vtkIOGeometry;vtkIOCore;vtkIOXMLParser;/usr/lib/x86_64-linux-gnu/libexpat.so;vtkFiltersAMR;vtkParallelCore;vtkIOLegacy;vtkFiltersFlowPaths;vtkFiltersGeneric;vtkFiltersHybrid;vtkImagingSources;vtkFiltersHyperTree;vtkFiltersImaging;vtkImagingGeneral;vtkFiltersModeling;vtkFiltersParallel;vtkFiltersParallelFlowPaths;vtkParallelMPI;vtkFiltersParallelGeometry;vtkFiltersParallelImaging;vtkFiltersParallelMPI;vtkFiltersParallelStatistics;vtkFiltersProgrammable;vtkFiltersPython;/usr/lib/x86_64-linux-gnu/libpython2.7.so;vtkWrappingPythonCore;vtkWrappingTools;vtkFiltersReebGraph;vtkFiltersSMP;vtkFiltersSelection;vtkFiltersTexture;vtkFiltersVerdict;verdict;vtkGUISupportQt;vtkInteractionStyle;vtkRenderingOpenGL;vtkImagingHybrid;vtkIOImage;vtkmetaio;/usr/lib/x86_64-linux-gnu/libjpeg.so;/usr/lib/x86_64-linux-gnu/libpng.so;/usr/lib/x86_64-linux-gnu/libtiff.so;vtkGUISupportQtOpenGL;vtkGUISupportQtSQL;vtkIOSQL;sqlite3;vtkGUISupportQtWebkit;vtkViewsQt;vtkViewsInfovis;vtkInfovisLayout;vtkInfovisBoostGraphAlgorithms;vtkRenderingLabel;vtkViewsCore;vtkInteractionWidgets;vtkRenderingAnnotation;vtkImagingColor;vtkRenderingVolume;vtkGeovisCore;/usr/lib/x86_64-linux-gnu/libproj.so;vtkIOAMR;/usr/lib/x86_64-linux-gnu/hdf5/openmpi/libhdf5.so;/usr/lib/x86_64-linux-gnu/libsz.so;/usr/lib/x86_64-linux-gnu/libdl.so;/usr/lib/x86_64-linux-gnu/libm.so;/usr/lib/x86_64-linux-gnu/openmpi/lib/libmpi.so;vtkIOEnSight;vtkIOExodus;vtkexoIIc;/usr/lib/x86_64-linux-gnu/libnetcdf_c++.so;/usr/lib/x86_64-linux-gnu/libnetcdf.so;vtkIOExport;vtkRenderingGL2PS;vtkRenderingContextOpenGL;/usr/lib/x86_64-linux-gnu/libgl2ps.so;vtkIOFFMPEG;vtkIOMovie;/usr/lib/x86_64-linux-gnu/libtheoraenc.so;/usr/lib/x86_64-linux-gnu/libtheoradec.so;/usr/lib/x86_64-linux-gnu/libogg.so;vtkIOGDAL;vtkIOGeoJSON;vtkIOImport;vtkIOInfovis;/usr/lib/x86_64-linux-gnu/libxml2.so;vtkIOLSDyna;vtkIOMINC;vtkIOMPIImage;vtkIOMPIParallel;vtkIOParallel;vtkIONetCDF;/usr/lib/x86_64-linux-gnu/libjsoncpp.so;vtkIOMySQL;vtkIOODBC;vtkIOPLY;vtkIOParallelExodus;vtkIOParallelLSDyna;vtkIOParallelNetCDF;vtkIOParallelXML;vtkIOPostgreSQL;vtkIOVPIC;VPIC;vtkIOVideo;vtkIOXdmf2;vtkxdmf2;vtkImagingMath;vtkImagingMorphological;vtkImagingStatistics;vtkImagingStencil;vtkInteractionImage;vtkLocalExample;vtkParallelMPI4Py;vtkPythonInterpreter;vtkRenderingExternal;vtkRenderingFreeTypeFontConfig;vtkRenderingImage;vtkRenderingLIC;vtkRenderingLOD;vtkRenderingMatplotlib;vtkRenderingParallel;vtkRenderingParallelLIC;vtkRenderingQt;vtkRenderingVolumeAMR;vtkRenderingVolumeOpenGL;vtkTestingGenericBridge;vtkTestingIOSQL;vtkTestingRendering;vtkViewsContext2D;vtkViewsGeovis;vtkWrappingJava][/usr/include/pcl-1.8;/usr/include/eigen3;/usr/include;/usr/include/ni;/usr/include/openni2;/usr/include/vtk-6.3;/usr/include/freetype2;/usr/lib/x86_64-linux-gnu/openmpi/include/openmpi;/usr/lib/x86_64-linux-gnu/openmpi/include/openmpi/opal/mca/event/libevent2022/libevent;/usr/lib/x86_64-linux-gnu/openmpi/include/openmpi/opal/mca/event/libevent2022/libevent/include;/usr/lib/x86_64-linux-gnu/openmpi/include;/usr/include/python2.7;/usr/include/x86_64-linux-gnu;/usr/include/hdf5/openmpi;/usr/include/libxml2;/usr/include/jsoncpp;/usr/include/tcl][v()]
//Details about finding PCL_2D
FIND_PACKAGE_MESSAGE_DETAILS_PCL_2D:INTERNAL=[/usr/include/pcl-1.8][v()]
//Details about finding PCL_APPS
FIND_PACKAGE_MESSAGE_DETAILS_PCL_APPS:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_apps.so][/usr/include/pcl-1.8][v()]
//Details about finding PCL_COMMON
FIND_PACKAGE_MESSAGE_DETAILS_PCL_COMMON:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_common.so][/usr/include/pcl-1.8][v()]
//Details about finding PCL_FEATURES
FIND_PACKAGE_MESSAGE_DETAILS_PCL_FEATURES:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_features.so][/usr/include/pcl-1.8][v()]
//Details about finding PCL_FILTERS
FIND_PACKAGE_MESSAGE_DETAILS_PCL_FILTERS:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_filters.so][/usr/include/pcl-1.8][v()]
//Details about finding PCL_GEOMETRY
FIND_PACKAGE_MESSAGE_DETAILS_PCL_GEOMETRY:INTERNAL=[/usr/include/pcl-1.8][v()]
//Details about finding PCL_IN_HAND_SCANNER
FIND_PACKAGE_MESSAGE_DETAILS_PCL_IN_HAND_SCANNER:INTERNAL=[/usr/include/pcl-1.8][v()]
//Details about finding PCL_IO
FIND_PACKAGE_MESSAGE_DETAILS_PCL_IO:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_io.so][/usr/include/pcl-1.8][v()]
//Details about finding PCL_KDTREE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_KDTREE:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_kdtree.so][/usr/include/pcl-1.8][v()]
//Details about finding PCL_KEYPOINTS
FIND_PACKAGE_MESSAGE_DETAILS_PCL_KEYPOINTS:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_keypoints.so][/usr/include/pcl-1.8][v()]
//Details about finding PCL_ML
FIND_PACKAGE_MESSAGE_DETAILS_PCL_ML:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_ml.so][/usr/include/pcl-1.8][v()]
//Details about finding PCL_MODELER
FIND_PACKAGE_MESSAGE_DETAILS_PCL_MODELER:INTERNAL=[/usr/include/pcl-1.8][v()]
//Details about finding PCL_OCTREE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_OCTREE:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_octree.so][/usr/include/pcl-1.8][v()]
//Details about finding PCL_OUTOFCORE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_OUTOFCORE:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_outofcore.so][/usr/include/pcl-1.8][v()]
//Details about finding PCL_PEOPLE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_PEOPLE:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_people.so][/usr/include/pcl-1.8][v()]
//Details about finding PCL_POINT_CLOUD_EDITOR
FIND_PACKAGE_MESSAGE_DETAILS_PCL_POINT_CLOUD_EDITOR:INTERNAL=[/usr/include/pcl-1.8][v()]
//Details about finding PCL_RECOGNITION
FIND_PACKAGE_MESSAGE_DETAILS_PCL_RECOGNITION:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_recognition.so][/usr/include/pcl-1.8][v()]
//Details about finding PCL_REGISTRATION
FIND_PACKAGE_MESSAGE_DETAILS_PCL_REGISTRATION:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_registration.so][/usr/include/pcl-1.8][v()]
//Details about finding PCL_SAMPLE_CONSENSUS
FIND_PACKAGE_MESSAGE_DETAILS_PCL_SAMPLE_CONSENSUS:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so][/usr/include/pcl-1.8][v()]
//Details about finding PCL_SEARCH
FIND_PACKAGE_MESSAGE_DETAILS_PCL_SEARCH:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_search.so][/usr/include/pcl-1.8][v()]
//Details about finding PCL_SEGMENTATION
FIND_PACKAGE_MESSAGE_DETAILS_PCL_SEGMENTATION:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_segmentation.so][/usr/include/pcl-1.8][v()]
//Details about finding PCL_STEREO
FIND_PACKAGE_MESSAGE_DETAILS_PCL_STEREO:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_stereo.so][/usr/include/pcl-1.8][v()]
//Details about finding PCL_SURFACE
FIND_PACKAGE_MESSAGE_DETAILS_PCL_SURFACE:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_surface.so][/usr/include/pcl-1.8][v()]
//Details about finding PCL_TRACKING
FIND_PACKAGE_MESSAGE_DETAILS_PCL_TRACKING:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_tracking.so][/usr/include/pcl-1.8][v()]
//Details about finding PCL_VISUALIZATION
FIND_PACKAGE_MESSAGE_DETAILS_PCL_VISUALIZATION:INTERNAL=[/usr/lib/x86_64-linux-gnu/libpcl_visualization.so][/usr/include/pcl-1.8][v()]
//Details about finding SuiteSparse
FIND_PACKAGE_MESSAGE_DETAILS_SuiteSparse:INTERNAL=[/usr/include/suitesparse][/usr/lib/x86_64-linux-gnu/libamd.so][/usr/include/suitesparse][/usr/lib/x86_64-linux-gnu/libcamd.so][/usr/include/suitesparse][/usr/lib/x86_64-linux-gnu/libccolamd.so][/usr/include/suitesparse][/usr/lib/x86_64-linux-gnu/libcholmod.so][/usr/include/suitesparse][/usr/lib/x86_64-linux-gnu/libcolamd.so][/usr/include/suitesparse][/usr/lib/x86_64-linux-gnu/libspqr.so][/usr/include/suitesparse][/usr/lib/x86_64-linux-gnu/libsuitesparseconfig.so][cfound components:  AMD CAMD CCOLAMD CHOLMOD COLAMD SPQR Config ][v5.1.2(5.1.2)]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//Details about finding eigen
FIND_PACKAGE_MESSAGE_DETAILS_eigen:INTERNAL=[/usr/include/eigen3][v()]
//Details about finding openni
FIND_PACKAGE_MESSAGE_DETAILS_openni:INTERNAL=[/usr/lib/libOpenNI.so][/usr/include/ni][v()]
//Details about finding qhull
FIND_PACKAGE_MESSAGE_DETAILS_qhull:INTERNAL=[/usr/lib/x86_64-linux-gnu/libqhull.so][/usr/include][v()]
//ADVANCED property for variable: GLOG_INCLUDE_DIR
GLOG_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GLOG_LIBRARY
GLOG_LIBRARY-ADVANCED:INTERNAL=1
//Have library rt
HAVE_LIBRT:INTERNAL=1
//ADVANCED property for variable: LAPACK_Accelerate_LIBRARY
LAPACK_Accelerate_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: LAPACK_goto2_LIBRARY
LAPACK_goto2_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: LAPACK_lapack_LIBRARY
LAPACK_lapack_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: LAPACK_lapack_WORKS
LAPACK_lapack_WORKS-ADVANCED:INTERNAL=1
//Have function cheev_
LAPACK_lapack_WORKS:INTERNAL=1
//ADVANCED property for variable: LAPACK_openblas_LIBRARY
LAPACK_openblas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: LAPACK_vecLib_LIBRARY
LAPACK_vecLib_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: METIS_INCLUDE_DIR
METIS_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: METIS_LIBRARY_DEBUG
METIS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: METIS_LIBRARY_RELEASE
METIS_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_EGL_INCLUDE_DIR
OPENGL_EGL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_GLX_INCLUDE_DIR
OPENGL_GLX_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_INCLUDE_DIR
OPENGL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_egl_LIBRARY
OPENGL_egl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_gl_LIBRARY
OPENGL_gl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_glu_LIBRARY
OPENGL_glu_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_glx_LIBRARY
OPENGL_glx_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_opengl_LIBRARY
OPENGL_opengl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_xmesa_INCLUDE_DIR
OPENGL_xmesa_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_APPS_LIBRARY
PCL_APPS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_APPS_LIBRARY_DEBUG
PCL_APPS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_COMMON_LIBRARY
PCL_COMMON_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_COMMON_LIBRARY_DEBUG
PCL_COMMON_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FEATURES_LIBRARY
PCL_FEATURES_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FEATURES_LIBRARY_DEBUG
PCL_FEATURES_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FILTERS_LIBRARY
PCL_FILTERS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_FILTERS_LIBRARY_DEBUG
PCL_FILTERS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_IO_LIBRARY
PCL_IO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_IO_LIBRARY_DEBUG
PCL_IO_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KDTREE_LIBRARY
PCL_KDTREE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KDTREE_LIBRARY_DEBUG
PCL_KDTREE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KEYPOINTS_LIBRARY
PCL_KEYPOINTS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_KEYPOINTS_LIBRARY_DEBUG
PCL_KEYPOINTS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_ML_LIBRARY
PCL_ML_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_ML_LIBRARY_DEBUG
PCL_ML_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OCTREE_LIBRARY
PCL_OCTREE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OCTREE_LIBRARY_DEBUG
PCL_OCTREE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OUTOFCORE_LIBRARY
PCL_OUTOFCORE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_OUTOFCORE_LIBRARY_DEBUG
PCL_OUTOFCORE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_PEOPLE_LIBRARY
PCL_PEOPLE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_PEOPLE_LIBRARY_DEBUG
PCL_PEOPLE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_RECOGNITION_LIBRARY
PCL_RECOGNITION_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_RECOGNITION_LIBRARY_DEBUG
PCL_RECOGNITION_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_REGISTRATION_LIBRARY
PCL_REGISTRATION_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_REGISTRATION_LIBRARY_DEBUG
PCL_REGISTRATION_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SAMPLE_CONSENSUS_LIBRARY
PCL_SAMPLE_CONSENSUS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SAMPLE_CONSENSUS_LIBRARY_DEBUG
PCL_SAMPLE_CONSENSUS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEARCH_LIBRARY
PCL_SEARCH_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEARCH_LIBRARY_DEBUG
PCL_SEARCH_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEGMENTATION_LIBRARY
PCL_SEGMENTATION_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SEGMENTATION_LIBRARY_DEBUG
PCL_SEGMENTATION_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_STEREO_LIBRARY
PCL_STEREO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_STEREO_LIBRARY_DEBUG
PCL_STEREO_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SURFACE_LIBRARY
PCL_SURFACE_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_SURFACE_LIBRARY_DEBUG
PCL_SURFACE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_TRACKING_LIBRARY
PCL_TRACKING_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_TRACKING_LIBRARY_DEBUG
PCL_TRACKING_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_VISUALIZATION_LIBRARY
PCL_VISUALIZATION_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PCL_VISUALIZATION_LIBRARY_DEBUG
PCL_VISUALIZATION_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
PC_EIGEN_CFLAGS:INTERNAL=-I/usr/include/eigen3
PC_EIGEN_CFLAGS_I:INTERNAL=
PC_EIGEN_CFLAGS_OTHER:INTERNAL=
PC_EIGEN_FOUND:INTERNAL=1
PC_EIGEN_INCLUDEDIR:INTERNAL=
PC_EIGEN_INCLUDE_DIRS:INTERNAL=/usr/include/eigen3
PC_EIGEN_LDFLAGS:INTERNAL=
PC_EIGEN_LDFLAGS_OTHER:INTERNAL=
PC_EIGEN_LIBDIR:INTERNAL=
PC_EIGEN_LIBRARIES:INTERNAL=
PC_EIGEN_LIBRARY_DIRS:INTERNAL=
PC_EIGEN_LIBS:INTERNAL=
PC_EIGEN_LIBS_L:INTERNAL=
PC_EIGEN_LIBS_OTHER:INTERNAL=
PC_EIGEN_LIBS_PATHS:INTERNAL=
PC_EIGEN_PREFIX:INTERNAL=/usr
PC_EIGEN_STATIC_CFLAGS:INTERNAL=-I/usr/include/eigen3
PC_EIGEN_STATIC_CFLAGS_I:INTERNAL=
PC_EIGEN_STATIC_CFLAGS_OTHER:INTERNAL=
PC_EIGEN_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/eigen3
PC_EIGEN_STATIC_LDFLAGS:INTERNAL=
PC_EIGEN_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_EIGEN_STATIC_LIBDIR:INTERNAL=
PC_EIGEN_STATIC_LIBRARIES:INTERNAL=
PC_EIGEN_STATIC_LIBRARY_DIRS:INTERNAL=
PC_EIGEN_STATIC_LIBS:INTERNAL=
PC_EIGEN_STATIC_LIBS_L:INTERNAL=
PC_EIGEN_STATIC_LIBS_OTHER:INTERNAL=
PC_EIGEN_STATIC_LIBS_PATHS:INTERNAL=
PC_EIGEN_VERSION:INTERNAL=3.3.4
PC_EIGEN_eigen3_INCLUDEDIR:INTERNAL=
PC_EIGEN_eigen3_LIBDIR:INTERNAL=
PC_EIGEN_eigen3_PREFIX:INTERNAL=
PC_EIGEN_eigen3_VERSION:INTERNAL=
PC_FLANN_CFLAGS:INTERNAL=
PC_FLANN_CFLAGS_I:INTERNAL=
PC_FLANN_CFLAGS_OTHER:INTERNAL=
PC_FLANN_FOUND:INTERNAL=1
PC_FLANN_INCLUDEDIR:INTERNAL=/usr/include
PC_FLANN_INCLUDE_DIRS:INTERNAL=
PC_FLANN_LDFLAGS:INTERNAL=-lflann;-lflann_cpp
PC_FLANN_LDFLAGS_OTHER:INTERNAL=
PC_FLANN_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_FLANN_LIBRARIES:INTERNAL=flann;flann_cpp
PC_FLANN_LIBRARY_DIRS:INTERNAL=
PC_FLANN_LIBS:INTERNAL=
PC_FLANN_LIBS_L:INTERNAL=
PC_FLANN_LIBS_OTHER:INTERNAL=
PC_FLANN_LIBS_PATHS:INTERNAL=
PC_FLANN_PREFIX:INTERNAL=/usr
PC_FLANN_STATIC_CFLAGS:INTERNAL=
PC_FLANN_STATIC_CFLAGS_I:INTERNAL=
PC_FLANN_STATIC_CFLAGS_OTHER:INTERNAL=
PC_FLANN_STATIC_INCLUDE_DIRS:INTERNAL=
PC_FLANN_STATIC_LDFLAGS:INTERNAL=-lflann;-lflann_cpp
PC_FLANN_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_FLANN_STATIC_LIBDIR:INTERNAL=
PC_FLANN_STATIC_LIBRARIES:INTERNAL=flann;flann_cpp
PC_FLANN_STATIC_LIBRARY_DIRS:INTERNAL=
PC_FLANN_STATIC_LIBS:INTERNAL=
PC_FLANN_STATIC_LIBS_L:INTERNAL=
PC_FLANN_STATIC_LIBS_OTHER:INTERNAL=
PC_FLANN_STATIC_LIBS_PATHS:INTERNAL=
PC_FLANN_VERSION:INTERNAL=1.9.1
PC_FLANN_flann_INCLUDEDIR:INTERNAL=
PC_FLANN_flann_LIBDIR:INTERNAL=
PC_FLANN_flann_PREFIX:INTERNAL=
PC_FLANN_flann_VERSION:INTERNAL=
PC_OPENNI2_CFLAGS:INTERNAL=-I/usr/include/openni2
PC_OPENNI2_CFLAGS_I:INTERNAL=
PC_OPENNI2_CFLAGS_OTHER:INTERNAL=
PC_OPENNI2_FOUND:INTERNAL=1
PC_OPENNI2_INCLUDEDIR:INTERNAL=/usr/include/openni2
PC_OPENNI2_INCLUDE_DIRS:INTERNAL=/usr/include/openni2
PC_OPENNI2_LDFLAGS:INTERNAL=-lOpenNI2
PC_OPENNI2_LDFLAGS_OTHER:INTERNAL=
PC_OPENNI2_LIBDIR:INTERNAL=/usr/lib
PC_OPENNI2_LIBRARIES:INTERNAL=OpenNI2
PC_OPENNI2_LIBRARY_DIRS:INTERNAL=
PC_OPENNI2_LIBS:INTERNAL=
PC_OPENNI2_LIBS_L:INTERNAL=
PC_OPENNI2_LIBS_OTHER:INTERNAL=
PC_OPENNI2_LIBS_PATHS:INTERNAL=
PC_OPENNI2_PREFIX:INTERNAL=/usr
PC_OPENNI2_STATIC_CFLAGS:INTERNAL=-I/usr/include/openni2
PC_OPENNI2_STATIC_CFLAGS_I:INTERNAL=
PC_OPENNI2_STATIC_CFLAGS_OTHER:INTERNAL=
PC_OPENNI2_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/openni2
PC_OPENNI2_STATIC_LDFLAGS:INTERNAL=-lOpenNI2
PC_OPENNI2_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_OPENNI2_STATIC_LIBDIR:INTERNAL=
PC_OPENNI2_STATIC_LIBRARIES:INTERNAL=OpenNI2
PC_OPENNI2_STATIC_LIBRARY_DIRS:INTERNAL=
PC_OPENNI2_STATIC_LIBS:INTERNAL=
PC_OPENNI2_STATIC_LIBS_L:INTERNAL=
PC_OPENNI2_STATIC_LIBS_OTHER:INTERNAL=
PC_OPENNI2_STATIC_LIBS_PATHS:INTERNAL=
PC_OPENNI2_VERSION:INTERNAL=2.2.0.3
PC_OPENNI2_libopenni2_INCLUDEDIR:INTERNAL=
PC_OPENNI2_libopenni2_LIBDIR:INTERNAL=
PC_OPENNI2_libopenni2_PREFIX:INTERNAL=
PC_OPENNI2_libopenni2_VERSION:INTERNAL=
PC_OPENNI_CFLAGS:INTERNAL=-I/usr/include/ni
PC_OPENNI_CFLAGS_I:INTERNAL=
PC_OPENNI_CFLAGS_OTHER:INTERNAL=
PC_OPENNI_FOUND:INTERNAL=1
PC_OPENNI_INCLUDEDIR:INTERNAL=/usr/include/ni
PC_OPENNI_INCLUDE_DIRS:INTERNAL=/usr/include/ni
PC_OPENNI_LDFLAGS:INTERNAL=-lOpenNI
PC_OPENNI_LDFLAGS_OTHER:INTERNAL=
PC_OPENNI_LIBDIR:INTERNAL=/usr/lib
PC_OPENNI_LIBRARIES:INTERNAL=OpenNI
PC_OPENNI_LIBRARY_DIRS:INTERNAL=
PC_OPENNI_LIBS:INTERNAL=
PC_OPENNI_LIBS_L:INTERNAL=
PC_OPENNI_LIBS_OTHER:INTERNAL=
PC_OPENNI_LIBS_PATHS:INTERNAL=
PC_OPENNI_PREFIX:INTERNAL=/usr
PC_OPENNI_STATIC_CFLAGS:INTERNAL=-I/usr/include/ni
PC_OPENNI_STATIC_CFLAGS_I:INTERNAL=
PC_OPENNI_STATIC_CFLAGS_OTHER:INTERNAL=
PC_OPENNI_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/ni
PC_OPENNI_STATIC_LDFLAGS:INTERNAL=-lOpenNI
PC_OPENNI_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_OPENNI_STATIC_LIBDIR:INTERNAL=
PC_OPENNI_STATIC_LIBRARIES:INTERNAL=OpenNI
PC_OPENNI_STATIC_LIBRARY_DIRS:INTERNAL=
PC_OPENNI_STATIC_LIBS:INTERNAL=
PC_OPENNI_STATIC_LIBS_L:INTERNAL=
PC_OPENNI_STATIC_LIBS_OTHER:INTERNAL=
PC_OPENNI_STATIC_LIBS_PATHS:INTERNAL=
PC_OPENNI_VERSION:INTERNAL=1.5.4.0
PC_OPENNI_libopenni_INCLUDEDIR:INTERNAL=
PC_OPENNI_libopenni_LIBDIR:INTERNAL=
PC_OPENNI_libopenni_PREFIX:INTERNAL=
PC_OPENNI_libopenni_VERSION:INTERNAL=
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: RSSDK_LIBRARY
RSSDK_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: RSSDK_LIBRARY_DEBUG
RSSDK_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SuiteSparse_AMD_INCLUDE_DIR
SuiteSparse_AMD_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SuiteSparse_AMD_LIBRARY
SuiteSparse_AMD_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SuiteSparse_CAMD_INCLUDE_DIR
SuiteSparse_CAMD_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SuiteSparse_CAMD_LIBRARY
SuiteSparse_CAMD_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SuiteSparse_CCOLAMD_INCLUDE_DIR
SuiteSparse_CCOLAMD_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SuiteSparse_CCOLAMD_LIBRARY
SuiteSparse_CCOLAMD_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SuiteSparse_CHOLMOD_INCLUDE_DIR
SuiteSparse_CHOLMOD_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SuiteSparse_CHOLMOD_LIBRARY
SuiteSparse_CHOLMOD_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SuiteSparse_COLAMD_INCLUDE_DIR
SuiteSparse_COLAMD_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SuiteSparse_COLAMD_LIBRARY
SuiteSparse_COLAMD_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SuiteSparse_Config_INCLUDE_DIR
SuiteSparse_Config_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SuiteSparse_Config_LIBRARY
SuiteSparse_Config_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SuiteSparse_SPQR_INCLUDE_DIR
SuiteSparse_SPQR_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SuiteSparse_SPQR_LIBRARY
SuiteSparse_SPQR_LIBRARY-ADVANCED:INTERNAL=1
//Last used BOOST_INCLUDEDIR value.
_BOOST_INCLUDEDIR_LAST:INTERNAL=/usr/include
//Last used Boost_ADDITIONAL_VERSIONS value.
_Boost_ADDITIONAL_VERSIONS_LAST:INTERNAL=1.65.1;1.65;1.64.0;1.64;1.63.0;1.63;1.62.0;1.62;1.61.0;1.61;1.60.0;1.60;1.59.0;1.59;1.58.0;1.58;1.57.0;1.57;1.56.0;1.56;1.55.0;1.55;1.54.0;1.54;1.53.0;1.53;1.52.0;1.52;1.51.0;1.51;1.50.0;1.50;1.49.0;1.49;1.48.0;1.48;1.47.0;1.47
//Components requested for this build tree.
_Boost_COMPONENTS_SEARCHED:INTERNAL=atomic;chrono;date_time;filesystem;iostreams;program_options;regex;serialization;system;thread
//Last used Boost_INCLUDE_DIR value.
_Boost_INCLUDE_DIR_LAST:INTERNAL=/usr/include
//Last used Boost_LIBRARY_DIR_DEBUG value.
_Boost_LIBRARY_DIR_DEBUG_LAST:INTERNAL=/usr/lib/x86_64-linux-gnu
//Last used Boost_LIBRARY_DIR_RELEASE value.
_Boost_LIBRARY_DIR_RELEASE_LAST:INTERNAL=/usr/lib/x86_64-linux-gnu
//Last used Boost_NAMESPACE value.
_Boost_NAMESPACE_LAST:INTERNAL=boost
//Last used Boost_USE_MULTITHREADED value.
_Boost_USE_MULTITHREADED_LAST:INTERNAL=TRUE
__pkg_config_arguments_PC_EIGEN:INTERNAL=eigen3
__pkg_config_arguments_PC_FLANN:INTERNAL=flann
__pkg_config_arguments_PC_OPENNI:INTERNAL=libopenni
__pkg_config_arguments_PC_OPENNI2:INTERNAL=libopenni2
__pkg_config_checked_PC_EIGEN:INTERNAL=1
__pkg_config_checked_PC_FLANN:INTERNAL=1
__pkg_config_checked_PC_OPENNI:INTERNAL=1
__pkg_config_checked_PC_OPENNI2:INTERNAL=1
prefix_result:INTERNAL=/usr/lib/x86_64-linux-gnu

