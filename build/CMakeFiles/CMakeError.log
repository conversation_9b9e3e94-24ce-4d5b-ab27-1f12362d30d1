Determining if the pthread_create exist failed with the following output:
Change Dir: /home/<USER>/Desktop/RoadLib-master/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_af751/fast"
/usr/bin/make -f CMakeFiles/cmTC_af751.dir/build.make CMakeFiles/cmTC_af751.dir/build
make[1]: 进入目录“/home/<USER>/Desktop/RoadLib-master/build/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_af751.dir/CheckSymbolExists.c.o
/usr/bin/cc    -o CMakeFiles/cmTC_af751.dir/CheckSymbolExists.c.o   -c /home/<USER>/Desktop/RoadLib-master/build/CMakeFiles/CMakeTmp/CheckSymbolExists.c
Linking C executable cmTC_af751
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_af751.dir/link.txt --verbose=1
/usr/bin/cc      -rdynamic CMakeFiles/cmTC_af751.dir/CheckSymbolExists.c.o  -o cmTC_af751 
CMakeFiles/cmTC_af751.dir/CheckSymbolExists.c.o：在函数‘main’中：
CheckSymbolExists.c:(.text+0x1b)：对‘pthread_create’未定义的引用
collect2: error: ld returned 1 exit status
CMakeFiles/cmTC_af751.dir/build.make:97: recipe for target 'cmTC_af751' failed
make[1]: *** [cmTC_af751] Error 1
make[1]: 离开目录“/home/<USER>/Desktop/RoadLib-master/build/CMakeFiles/CMakeTmp”
Makefile:126: recipe for target 'cmTC_af751/fast' failed
make: *** [cmTC_af751/fast] Error 2

File /home/<USER>/Desktop/RoadLib-master/build/CMakeFiles/CMakeTmp/CheckSymbolExists.c:
/* */
#include <pthread.h>

int main(int argc, char** argv)
{
  (void)argv;
#ifndef pthread_create
  return ((int*)(&pthread_create))[argc];
#else
  (void)argc;
  return 0;
#endif
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /home/<USER>/Desktop/RoadLib-master/build/CMakeFiles/CMakeTmp

Run Build Command:"/usr/bin/make" "cmTC_b25a8/fast"
/usr/bin/make -f CMakeFiles/cmTC_b25a8.dir/build.make CMakeFiles/cmTC_b25a8.dir/build
make[1]: 进入目录“/home/<USER>/Desktop/RoadLib-master/build/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_b25a8.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_b25a8.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.10/Modules/CheckFunctionExists.c
Linking C executable cmTC_b25a8
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_b25a8.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create    -rdynamic CMakeFiles/cmTC_b25a8.dir/CheckFunctionExists.c.o  -o cmTC_b25a8 -lpthreads 
/usr/bin/ld: 找不到 -lpthreads
collect2: error: ld returned 1 exit status
CMakeFiles/cmTC_b25a8.dir/build.make:97: recipe for target 'cmTC_b25a8' failed
make[1]: *** [cmTC_b25a8] Error 1
make[1]: 离开目录“/home/<USER>/Desktop/RoadLib-master/build/CMakeFiles/CMakeTmp”
Makefile:126: recipe for target 'cmTC_b25a8/fast' failed
make: *** [cmTC_b25a8/fast] Error 2


