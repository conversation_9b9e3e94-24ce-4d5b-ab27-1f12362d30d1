#include "roadlib.h"
#include "gviewer.h"
#include "visualization.h"
#include <iostream>

gviewer viewer;
vector<VisualizedInstance> vis_instances;

int main(int argc, char *argv[])
{
    if(argc != 2)
    {
        std::cerr << "[ERROR] Usage: " << argv[0] << " MAP_FILE" << std::endl;
        exit(1);
    }
    
    string map_file(argv[1]);
    
    // Initialize viewer
    viewer.Show();
    
    // Load map
    RoadInstancePatchMap road_map;
    std::cout << "[INFO] Loading map from " << map_file << "..." << std::endl;
    road_map.loadMapFromFileBinaryRaw(map_file);
    road_map.unfreeze();
    
    std::cout << "[INFO] Map loaded successfully!" << std::endl;
    std::cout << "[INFO] Reference position: " << road_map.ref.transpose() << std::endl;
    
    // Visualize the map
    visualize_roadmap(road_map, vis_instances);
    viewer.SetInstances(vis_instances);
    
    std::cout << "[INFO] Map visualization ready. Press any key to exit..." << std::endl;
    
    // Keep the viewer running
    while (true)
    {
        // Keep the program running so the viewer stays open
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    return 0;
}
