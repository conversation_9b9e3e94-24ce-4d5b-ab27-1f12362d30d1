#!/usr/bin/env python3
"""
PNG to JPG Converter
将指定文件夹中的PNG格式图片批量转换为JPG格式
"""

import os
import sys
from PIL import Image
import argparse
from pathlib import Path

def convert_png_to_jpg(input_folder, output_folder=None, quality=95, remove_original=False):
    """
    将PNG图片转换为JPG格式
    
    Args:
        input_folder (str): 输入文件夹路径
        output_folder (str): 输出文件夹路径，如果为None则在原文件夹中保存
        quality (int): JPG图片质量 (1-100)
        remove_original (bool): 是否删除原始PNG文件
    """
    input_path = Path(input_folder)
    
    if not input_path.exists():
        print(f"错误: 输入文件夹 '{input_folder}' 不存在")
        return False
    
    # 如果没有指定输出文件夹，则使用输入文件夹
    if output_folder is None:
        output_path = input_path
    else:
        output_path = Path(output_folder)
        output_path.mkdir(parents=True, exist_ok=True)
    
    # 查找所有PNG文件
    png_files = list(input_path.glob("*.png"))
    
    if not png_files:
        print(f"在文件夹 '{input_folder}' 中没有找到PNG文件")
        return False
    
    print(f"找到 {len(png_files)} 个PNG文件")
    print(f"开始转换...")
    
    converted_count = 0
    failed_count = 0
    
    for png_file in png_files:
        try:
            # 打开PNG图片
            with Image.open(png_file) as img:
                # 如果图片有透明通道，转换为RGB模式
                if img.mode in ('RGBA', 'LA', 'P'):
                    # 创建白色背景
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 生成输出文件名
                jpg_filename = png_file.stem + '.jpg'
                jpg_path = output_path / jpg_filename
                
                # 保存为JPG格式
                img.save(jpg_path, 'JPEG', quality=quality, optimize=True)
                
                print(f"✓ 转换完成: {png_file.name} -> {jpg_filename}")
                converted_count += 1
                
                # 如果需要删除原始文件
                if remove_original:
                    png_file.unlink()
                    print(f"  删除原文件: {png_file.name}")
                
        except Exception as e:
            print(f"✗ 转换失败: {png_file.name} - {str(e)}")
            failed_count += 1
    
    print(f"\n转换完成!")
    print(f"成功转换: {converted_count} 个文件")
    if failed_count > 0:
        print(f"转换失败: {failed_count} 个文件")
    
    return True

def main():
    parser = argparse.ArgumentParser(description='将PNG图片批量转换为JPG格式')
    parser.add_argument('input_folder', help='输入文件夹路径')
    parser.add_argument('-o', '--output', help='输出文件夹路径 (默认为输入文件夹)')
    parser.add_argument('-q', '--quality', type=int, default=95, 
                       help='JPG图片质量 (1-100, 默认95)')
    parser.add_argument('-r', '--remove', action='store_true',
                       help='转换后删除原始PNG文件')
    
    args = parser.parse_args()
    
    # 验证质量参数
    if not 1 <= args.quality <= 100:
        print("错误: 图片质量必须在1-100之间")
        sys.exit(1)
    
    # 执行转换
    success = convert_png_to_jpg(
        input_folder=args.input_folder,
        output_folder=args.output,
        quality=args.quality,
        remove_original=args.remove
    )
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    # 如果直接运行脚本，可以在这里设置默认参数
    if len(sys.argv) == 1:
        # 默认转换test3/cam0文件夹
        default_folder = "/home/<USER>/Desktop/RoadLib-master/test3/cam0"
        print(f"使用默认文件夹: {default_folder}")
        
        success = convert_png_to_jpg(
            input_folder=default_folder,
            output_folder=None,  # 在原文件夹中保存
            quality=95,
            remove_original=False  # 不删除原文件
        )
        
        if not success:
            sys.exit(1)
    else:
        main()
