%YAML:1.0

cam0_calib: "cam0_pinhole.yaml"

IPM_WIDTH: 750 # for visualization
IPM_HEIGHT: 1850
IPM_RESO: 0.012

cg_alpha: -0.64
cg_theta: -12.62
cg_h: 0.8

body_T_cam0: !!opencv-matrix
   rows: 4
   cols: 4
   dt: d
   data: [ 0.99998, 0.00307, 0.00406, 0.02306,
           -0.00307, 1.0000, -0.00196, -0.00021,
           -0.00407, -0.00195, 0.99998, -0.00200,
           0.00000,0.00000,0.00000,1.00000]
t_start: 1721871340.026
t_end: 1721871376.989

patch.min_size: 50
patch.dashed_min_h: 1.35
patch.dashed_max_h: 10.0
patch.dashed_max_dist: 15.0
patch.guide_min_h: 0.0
patch.guide_max_h: 1000.0
patch.guide_max_dist:  30.0
patch.solid_max_dist: 15.0
patch.stop_max_dist:  10.0

need_smooth: 1
pose_smooth_window: 20
large_slope_thresold: 2.0
enable_vis_image: 1
enable_vis_3d: 1

localization.every_n_frames: 5
localization.force_last_n_frames: 2
localization.max_windowsize: 100
localization.min_keyframe_dist: 1.0
localization.max_strict_match_dist: 1.0
localization.solid_sample_interval: 3.0
