#!/usr/bin/env python3
"""
Convert RoadLib binary map to Shapefile for QGIS visualization
"""

import struct
import numpy as np
import geopandas as gpd
from shapely.geometry import LineString, Polygon, Point
import pandas as pd
import argparse
import os

class Patch:
    def __init__(self):
        self.next_id = 0
        self.id = 0
        self.road_class = 0
        self.line_valid = False
        self.frozen = False
        self.merged = False
        self.valid_add_to_map = False
        self.mean_metric = []
        self.cov_metric = []
        self.direction_metric = []
        self.eigen_value_metric = []
        self.line_points = []
        self.mean_uncertainty = []
        self.line_points_uncertainty = []
        self.percept_distance = 0.0
        self.b_points = [[], [], [], []]
        self.b_unc = [0.0, 0.0, 0.0, 0.0]

def load_roadlib_map(map_file):
    """Load RoadLib binary map file"""
    print(f"[INFO] Loading map from {map_file}...")
    
    with open(map_file, 'rb') as fp:
        buff = fp.read()
    
    offset = 0
    all_patches = {}
    
    # Read reference position
    pos_ref = struct.unpack_from('ddd', buff, offset)
    offset += 8 * 3
    print(f"[INFO] Reference position: {pos_ref}")
    
    # Read number of patch classes
    count = struct.unpack_from('i', buff, offset)[0]
    offset += 8 * 1
    print(f"[INFO] Number of patch classes: {count}")
    
    for iclass in range(count):
        road_class = struct.unpack_from('i', buff, offset)[0]
        offset += 4 * 1
        pcount = struct.unpack_from('i', buff, offset)[0]
        offset += 8 * 1
        print(f"[INFO] Class {road_class}: {pcount} patches")
        
        all_patches[road_class] = []
        
        for ipatch in range(pcount):
            patch = Patch()
            patch.next_id = struct.unpack_from('q', buff, offset)[0]
            offset += 8 * 1
            patch.id = struct.unpack_from('q', buff, offset)[0]
            offset += 8 * 1
            patch.road_class = struct.unpack_from('i', buff, offset)[0]
            offset += 4 * 1
            patch.line_valid = struct.unpack_from('?', buff, offset)[0]
            offset += 1 * 1
            patch.frozen = struct.unpack_from('?', buff, offset)[0]
            offset += 1 * 1
            patch.merged = struct.unpack_from('?', buff, offset)[0]
            offset += 1 * 1
            patch.valid_add_to_map = struct.unpack_from('?', buff, offset)[0]
            offset += 1 * 1
            patch.mean_metric = struct.unpack_from('fff', buff, offset)
            offset += 4 * 3
            patch.b_points[0] = struct.unpack_from('fff', buff, offset)
            offset += 4 * 3
            patch.b_points[1] = struct.unpack_from('fff', buff, offset)
            offset += 4 * 3
            patch.b_points[2] = struct.unpack_from('fff', buff, offset)
            offset += 4 * 3
            patch.b_points[3] = struct.unpack_from('fff', buff, offset)
            offset += 4 * 3
            patch.b_unc[0] = struct.unpack_from('f', buff, offset)[0]
            offset += 4
            patch.b_unc[1] = struct.unpack_from('f', buff, offset)[0]
            offset += 4
            patch.b_unc[2] = struct.unpack_from('f', buff, offset)[0]
            offset += 4
            patch.b_unc[3] = struct.unpack_from('f', buff, offset)[0]
            offset += 4
            
            # Read line points
            lpcount = struct.unpack_from('i', buff, offset)[0]
            offset += 8 * 1
            for ilp in range(lpcount):
                lp = struct.unpack_from('fff', buff, offset)
                offset += 4 * 3
                patch.line_points.append(lp)
            
            patch.mean_uncertainty = struct.unpack_from('fffffffff', buff, offset)
            offset += 4 * 9
            
            lpcount = struct.unpack_from('i', buff, offset)[0]
            offset += 8 * 1
            for ilp in range(lpcount):
                lp_uncertainty = struct.unpack_from('fffffffff', buff, offset)
                offset += 4 * 9
                patch.line_points_uncertainty.append(lp_uncertainty)
            
            patch.percept_distance = struct.unpack_from('d', buff, offset)[0]
            offset += 8 * 1
            
            all_patches[road_class].append(patch)
    
    return all_patches, pos_ref

def convert_to_shapefile(all_patches, pos_ref, output_dir):
    """Convert patches to Shapefile format"""
    print(f"[INFO] Converting to Shapefile format...")
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Road class names mapping
    class_names = {
        0: "line_markings",
        1: "solid_areas", 
        2: "dashed_areas",
        3: "other_markings",
        4: "lane_boundaries"
    }
    
    for road_class, patches in all_patches.items():
        if not patches:
            continue
            
        class_name = class_names.get(road_class, f"class_{road_class}")
        print(f"[INFO] Processing {class_name} ({len(patches)} patches)...")
        
        geometries = []
        attributes = []
        
        for i, patch in enumerate(patches):
            if not patch.valid_add_to_map:
                continue
                
            # Create geometry based on patch type
            if road_class in [0, 4] and patch.line_valid and len(patch.line_points) >= 2:
                # Line markings and lane boundaries - create LineString
                coords = [(p[0], p[1]) for p in patch.line_points]
                geom = LineString(coords)
            elif road_class in [1, 2, 3] and len(patch.b_points) == 4:
                # Solid/dashed areas - create Polygon
                coords = [(p[0], p[1]) for p in patch.b_points]
                coords.append(coords[0])  # Close the polygon
                geom = Polygon(coords)
            else:
                # Fallback to point geometry
                geom = Point(patch.mean_metric[0], patch.mean_metric[1])
            
            geometries.append(geom)
            
            # Add attributes
            attributes.append({
                'patch_id': patch.id,
                'road_class': patch.road_class,
                'class_name': class_name,
                'line_valid': patch.line_valid,
                'frozen': patch.frozen,
                'merged': patch.merged,
                'mean_x': patch.mean_metric[0],
                'mean_y': patch.mean_metric[1],
                'mean_z': patch.mean_metric[2] if len(patch.mean_metric) > 2 else 0,
                'percept_dist': patch.percept_distance
            })
        
        if geometries:
            # Create GeoDataFrame
            gdf = gpd.GeoDataFrame(attributes, geometry=geometries)
            
            # Set coordinate reference system (assuming local metric coordinates)
            # You may need to adjust this based on your actual coordinate system
            gdf.crs = "EPSG:3857"  # Web Mercator, change if needed
            
            # Save to Shapefile
            output_file = os.path.join(output_dir, f"{class_name}.shp")
            gdf.to_file(output_file)
            print(f"[INFO] Saved {len(gdf)} features to {output_file}")

def main():
    parser = argparse.ArgumentParser(description='Convert RoadLib binary map to Shapefile')
    parser.add_argument('map_file', help='Input RoadLib binary map file (.bin)')
    parser.add_argument('-o', '--output', default='./qgis_output', 
                       help='Output directory for Shapefiles (default: ./qgis_output)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.map_file):
        print(f"[ERROR] Map file not found: {args.map_file}")
        return 1
    
    try:
        # Load the map
        all_patches, pos_ref = load_roadlib_map(args.map_file)
        
        # Convert to Shapefile
        convert_to_shapefile(all_patches, pos_ref, args.output)
        
        print(f"[INFO] Conversion completed successfully!")
        print(f"[INFO] Output files saved to: {args.output}")
        print(f"[INFO] You can now open these .shp files in QGIS")
        
        return 0
        
    except Exception as e:
        print(f"[ERROR] Conversion failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
