#!/usr/bin/env python3
"""
ZED 2i数据采集脚本
采集RGB图像、深度图像和里程计数据
"""

import pyzed.sl as sl
import cv2
import numpy as np
import os
import time
from datetime import datetime

class ZEDDataCollector:
    def __init__(self, output_dir="zed_data"):
        self.output_dir = output_dir
        self.setup_directories()
        
        # 初始化ZED相机
        self.zed = sl.Camera()
        self.init_params = sl.InitParameters()
        self.init_params.camera_resolution = sl.RESOLUTION.HD720
        self.init_params.camera_fps = 30
        self.init_params.depth_mode = sl.DEPTH_MODE.PERFORMANCE
        self.init_params.coordinate_units = sl.UNIT.METER
        
        # 启用位置跟踪
        self.tracking_params = sl.PositionalTrackingParameters()
        self.tracking_params.enable_imu_fusion = True
        
        # 图像容器
        self.image = sl.Mat()
        self.depth = sl.Mat()
        self.point_cloud = sl.Mat()
        
        # 位姿数据
        self.camera_pose = sl.Pose()
        
    def setup_directories(self):
        """创建输出目录"""
        dirs = ['cam0', 'depth', 'semantic']
        for d in dirs:
            os.makedirs(os.path.join(self.output_dir, d), exist_ok=True)
    
    def initialize_camera(self):
        """初始化相机"""
        err = self.zed.open(self.init_params)
        if err != sl.ERROR_CODE.SUCCESS:
            print(f"相机初始化失败: {err}")
            return False
            
        # 启用位置跟踪
        err = self.zed.enable_positional_tracking(self.tracking_params)
        if err != sl.ERROR_CODE.SUCCESS:
            print(f"位置跟踪启用失败: {err}")
            return False
            
        print("ZED相机初始化成功")
        return True
    
    def collect_data(self, duration_seconds=60):
        """采集数据"""
        if not self.initialize_camera():
            return
            
        # 准备文件
        stamp_file = open(os.path.join(self.output_dir, 'stamp.txt'), 'w')
        odo_file = open(os.path.join(self.output_dir, 'odo.txt'), 'w')
        gt_file = open(os.path.join(self.output_dir, 'gt.txt'), 'w')
        
        frame_count = 0
        start_time = time.time()
        
        print(f"开始采集数据，持续{duration_seconds}秒...")
        
        try:
            while (time.time() - start_time) < duration_seconds:
                # 获取新帧
                if self.zed.grab() == sl.ERROR_CODE.SUCCESS:
                    timestamp = time.time()
                    
                    # 获取RGB图像
                    self.zed.retrieve_image(self.image, sl.VIEW.LEFT)
                    image_np = self.image.get_data()
                    image_bgr = cv2.cvtColor(image_np, cv2.COLOR_RGBA2BGR)
                    
                    # 获取深度图像
                    self.zed.retrieve_image(self.depth, sl.VIEW.DEPTH)
                    depth_np = self.depth.get_data()
                    
                    # 获取位姿数据
                    tracking_state = self.zed.get_position(self.camera_pose)
                    
                    if tracking_state == sl.POSITIONAL_TRACKING_STATE.OK:
                        # 保存图像
                        img_filename = f"{frame_count:08d}_pred.png"
                        cv2.imwrite(os.path.join(self.output_dir, 'cam0', img_filename), image_bgr)
                        
                        # 保存深度图（可选）
                        depth_filename = f"{frame_count:08d}_depth.png"
                        cv2.imwrite(os.path.join(self.output_dir, 'depth', depth_filename), depth_np)
                        
                        # 获取位姿数据
                        pose_data = self.camera_pose.get_translation()
                        rotation_data = self.camera_pose.get_orientation()
                        
                        # 写入时间戳文件
                        stamp_file.write(f"{timestamp:.3f} {img_filename}\n")
                        
                        # 写入里程计文件 (timestamp tx ty tz qx qy qz qw)
                        odo_file.write(f"{timestamp:.3f} {pose_data.get()[0]:.6f} {pose_data.get()[1]:.6f} {pose_data.get()[2]:.6f} "
                                     f"{rotation_data.get()[0]:.8f} {rotation_data.get()[1]:.8f} {rotation_data.get()[2]:.8f} {rotation_data.get()[3]:.8f}\n")
                        
                        # GT文件与odo文件相同（如果没有外部真值的话）
                        gt_file.write(f"{timestamp:.3f} {pose_data.get()[0]:.6f} {pose_data.get()[1]:.6f} {pose_data.get()[2]:.6f} "
                                    f"{rotation_data.get()[0]:.8f} {rotation_data.get()[1]:.8f} {rotation_data.get()[2]:.8f} {rotation_data.get()[3]:.8f}\n")
                        
                        frame_count += 1
                        
                        if frame_count % 30 == 0:  # 每秒打印一次进度
                            print(f"已采集 {frame_count} 帧，位置: ({pose_data.get()[0]:.3f}, {pose_data.get()[1]:.3f}, {pose_data.get()[2]:.3f})")
                    
                    # 显示图像（可选）
                    cv2.imshow("ZED Camera", image_bgr)
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        break
                        
        except KeyboardInterrupt:
            print("用户中断采集")
        
        finally:
            # 清理资源
            stamp_file.close()
            odo_file.close()
            gt_file.close()
            self.zed.close()
            cv2.destroyAllWindows()
            
        print(f"数据采集完成，共采集 {frame_count} 帧")
        print(f"数据保存在: {self.output_dir}")

def main():
    collector = ZEDDataCollector("test_zed_data")
    collector.collect_data(duration_seconds=30)  # 采集30秒数据

if __name__ == "__main__":
    main()
