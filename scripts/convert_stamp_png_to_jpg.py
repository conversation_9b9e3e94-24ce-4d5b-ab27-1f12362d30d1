#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
将stamp.txt文件中的.png扩展名批量替换为.jpg
"""

import os
import sys

def convert_stamp_file(input_file, output_file=None):
    """
    将stamp.txt文件中的.png扩展名替换为.jpg
    
    Args:
        input_file: 输入的stamp.txt文件路径
        output_file: 输出文件路径，如果为None则覆盖原文件
    """
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print("错误: 文件不存在 - " + input_file)
        return False
    
    # 如果没有指定输出文件，则覆盖原文件
    if output_file is None:
        output_file = input_file
    
    try:
        # 读取原文件
        with open(input_file, 'r') as f:
            lines = f.readlines()
        
        # 处理每一行
        converted_lines = []
        conversion_count = 0
        
        for line_num, line in enumerate(lines, 1):
            original_line = line.strip()
            
            # 替换.png为.jpg (不区分大小写)
            if '.png' in line.lower():
                # 替换所有可能的大小写组合
                new_line = line.replace('.png', '.jpg')
                new_line = new_line.replace('.PNG', '.jpg')
                new_line = new_line.replace('.Png', '.jpg')
                new_line = new_line.replace('.pNG', '.jpg')
                new_line = new_line.replace('.PNg', '.jpg')
                new_line = new_line.replace('.pNg', '.jpg')
                new_line = new_line.replace('.PnG', '.jpg')
                new_line = new_line.replace('.pnG', '.jpg')
                
                conversion_count += 1
                print("第" + str(line_num) + "行: " + original_line + " -> " + new_line.strip())
                converted_lines.append(new_line)
            else:
                converted_lines.append(line)
        
        # 写入输出文件
        with open(output_file, 'w') as f:
            f.writelines(converted_lines)
        
        print("")
        print("转换完成!")
        print("总共处理了 " + str(len(lines)) + " 行")
        print("转换了 " + str(conversion_count) + " 个文件名")
        print("输出文件: " + output_file)
        
        return True
        
    except Exception as e:
        print("错误: " + str(e))
        return False

def main():
    """主函数"""
    
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("用法:")
        print("  python " + sys.argv[0] + " <stamp.txt文件路径> [输出文件路径]")
        print("")
        print("示例:")
        print("  python " + sys.argv[0] + " test3/stamp.txt")
        print("  python " + sys.argv[0] + " test3/stamp.txt test3/stamp_jpg.txt")
        return
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 执行转换
    success = convert_stamp_file(input_file, output_file)
    
    if success:
        print("转换成功完成!")
    else:
        print("转换失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
