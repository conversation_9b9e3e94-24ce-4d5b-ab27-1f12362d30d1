#!/usr/bin/env python3
"""
使用ROS从ZED相机采集数据
需要安装zed-ros-wrapper
"""

import rospy
import cv2
import numpy as np
from sensor_msgs.msg import Image, CompressedImage
from geometry_msgs.msg import PoseStamped
from nav_msgs.msg import Odometry
from cv_bridge import CvBridge
import os
import time

class ROSZEDCollector:
    def __init__(self, output_dir="ros_zed_data"):
        self.output_dir = output_dir
        self.setup_directories()
        
        self.bridge = CvBridge()
        self.frame_count = 0
        
        # 文件句柄
        self.stamp_file = open(os.path.join(self.output_dir, 'stamp.txt'), 'w')
        self.odo_file = open(os.path.join(self.output_dir, 'odo.txt'), 'w')
        self.gt_file = open(os.path.join(self.output_dir, 'gt.txt'), 'w')
        
        # ROS订阅者
        self.image_sub = rospy.Subscriber('/zed2i/zed_node/left/image_rect_color', 
                                         Image, self.image_callback)
        self.odom_sub = rospy.Subscriber('/zed2i/zed_node/odom', 
                                        Odometry, self.odom_callback)
        
        self.current_image = None
        self.current_odom = None
        
    def setup_directories(self):
        """创建输出目录"""
        dirs = ['cam0', 'semantic']
        for d in dirs:
            os.makedirs(os.path.join(self.output_dir, d), exist_ok=True)
    
    def image_callback(self, msg):
        """图像回调函数"""
        try:
            cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
            self.current_image = cv_image
            self.process_frame(msg.header.stamp.to_sec())
        except Exception as e:
            rospy.logerr(f"图像处理错误: {e}")
    
    def odom_callback(self, msg):
        """里程计回调函数"""
        self.current_odom = msg
    
    def process_frame(self, timestamp):
        """处理帧数据"""
        if self.current_image is not None and self.current_odom is not None:
            # 保存图像
            img_filename = f"{self.frame_count:08d}_pred.png"
            cv2.imwrite(os.path.join(self.output_dir, 'cam0', img_filename), self.current_image)
            
            # 写入时间戳
            self.stamp_file.write(f"{timestamp:.3f} {img_filename}\n")
            
            # 获取位姿数据
            pos = self.current_odom.pose.pose.position
            ori = self.current_odom.pose.pose.orientation
            
            # 写入里程计数据
            odo_line = f"{timestamp:.3f} {pos.x:.6f} {pos.y:.6f} {pos.z:.6f} {ori.x:.8f} {ori.y:.8f} {ori.z:.8f} {ori.w:.8f}\n"
            self.odo_file.write(odo_line)
            self.gt_file.write(odo_line)  # 假设odo就是gt
            
            self.frame_count += 1
            
            if self.frame_count % 30 == 0:
                rospy.loginfo(f"已处理 {self.frame_count} 帧")
    
    def cleanup(self):
        """清理资源"""
        self.stamp_file.close()
        self.odo_file.close()
        self.gt_file.close()

def main():
    rospy.init_node('zed_data_collector')
    collector = ROSZEDCollector()
    
    try:
        rospy.spin()
    except KeyboardInterrupt:
        pass
    finally:
        collector.cleanup()

if __name__ == "__main__":
    main()
