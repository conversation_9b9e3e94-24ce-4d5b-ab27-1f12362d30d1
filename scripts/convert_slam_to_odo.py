#!/usr/bin/env python3
"""
将SLAM系统输出的轨迹转换为RoadLib需要的odo.txt格式
支持多种SLAM系统的输出格式
"""

import numpy as np
import argparse
from scipy.spatial.transform import Rotation as R

def convert_tum_format(input_file, output_file):
    """
    转换TUM格式 (timestamp tx ty tz qx qy qz qw)
    这是最常见的格式，与RoadLib需要的格式相同
    """
    with open(input_file, 'r') as f_in, open(output_file, 'w') as f_out:
        for line in f_in:
            if line.startswith('#'):
                continue
            parts = line.strip().split()
            if len(parts) >= 8:
                # 直接复制，格式已经正确
                f_out.write(line)

def convert_kitti_format(input_file, output_file, timestamps_file):
    """
    转换KITTI格式的位姿矩阵
    input_file: 包含4x4变换矩阵的文件
    timestamps_file: 对应的时间戳文件
    """
    # 读取时间戳
    timestamps = []
    with open(timestamps_file, 'r') as f:
        for line in f:
            timestamps.append(float(line.strip()))
    
    with open(input_file, 'r') as f_in, open(output_file, 'w') as f_out:
        for i, line in enumerate(f_in):
            if i >= len(timestamps):
                break
                
            # 解析4x4矩阵的前3行（KITTI格式）
            values = list(map(float, line.strip().split()))
            if len(values) == 12:  # 3x4矩阵
                # 重构4x4矩阵
                T = np.eye(4)
                T[0, :] = values[0:4]
                T[1, :] = values[4:8]
                T[2, :] = values[8:12]
                
                # 提取平移
                tx, ty, tz = T[0, 3], T[1, 3], T[2, 3]
                
                # 提取旋转矩阵并转换为四元数
                rot_matrix = T[:3, :3]
                r = R.from_matrix(rot_matrix)
                qx, qy, qz, qw = r.as_quat()
                
                # 写入TUM格式
                timestamp = timestamps[i]
                f_out.write(f"{timestamp:.6f} {tx:.6f} {ty:.6f} {tz:.6f} {qx:.8f} {qy:.8f} {qz:.8f} {qw:.8f}\n")

def convert_euroc_format(input_file, output_file):
    """
    转换EuRoC格式 (timestamp, px, py, pz, qw, qx, qy, qz)
    注意：EuRoC的四元数顺序是 qw, qx, qy, qz
    """
    with open(input_file, 'r') as f_in, open(output_file, 'w') as f_out:
        for line in f_in:
            if line.startswith('#'):
                continue
            parts = line.strip().split(',')
            if len(parts) >= 8:
                timestamp = float(parts[0]) / 1e9  # 纳秒转秒
                tx, ty, tz = float(parts[1]), float(parts[2]), float(parts[3])
                qw, qx, qy, qz = float(parts[4]), float(parts[5]), float(parts[6]), float(parts[7])
                
                # 转换为TUM格式 (qx, qy, qz, qw)
                f_out.write(f"{timestamp:.6f} {tx:.6f} {ty:.6f} {tz:.6f} {qx:.8f} {qy:.8f} {qz:.8f} {qw:.8f}\n")

def convert_orbslam_format(input_file, output_file):
    """
    转换ORB-SLAM输出格式
    通常是 timestamp tx ty tz qx qy qz qw 格式
    """
    convert_tum_format(input_file, output_file)

def interpolate_poses(input_file, output_file, target_timestamps_file):
    """
    根据目标时间戳插值位姿
    用于将SLAM轨迹与图像时间戳对齐
    """
    # 读取原始轨迹
    poses = []
    with open(input_file, 'r') as f:
        for line in f:
            if line.startswith('#'):
                continue
            parts = line.strip().split()
            if len(parts) >= 8:
                timestamp = float(parts[0])
                pose = [float(x) for x in parts[1:8]]
                poses.append([timestamp] + pose)
    
    poses = np.array(poses)
    
    # 读取目标时间戳
    target_timestamps = []
    with open(target_timestamps_file, 'r') as f:
        for line in f:
            parts = line.strip().split()
            if len(parts) >= 1:
                target_timestamps.append(float(parts[0]))
    
    # 插值
    with open(output_file, 'w') as f_out:
        for target_t in target_timestamps:
            # 找到最近的两个位姿
            idx = np.searchsorted(poses[:, 0], target_t)
            
            if idx == 0:
                # 使用第一个位姿
                pose = poses[0, 1:]
            elif idx >= len(poses):
                # 使用最后一个位姿
                pose = poses[-1, 1:]
            else:
                # 线性插值平移部分
                t1, t2 = poses[idx-1, 0], poses[idx, 0]
                alpha = (target_t - t1) / (t2 - t1)
                
                trans1, trans2 = poses[idx-1, 1:4], poses[idx, 1:4]
                trans_interp = trans1 + alpha * (trans2 - trans1)
                
                # 球面插值四元数
                quat1 = poses[idx-1, 4:8]
                quat2 = poses[idx, 4:8]
                
                # 确保四元数在同一半球
                if np.dot(quat1, quat2) < 0:
                    quat2 = -quat2
                
                # SLERP插值
                r1 = R.from_quat(quat1)
                r2 = R.from_quat(quat2)
                r_interp = R.from_quat(quat1).slerp(R.from_quat(quat2), alpha)
                quat_interp = r_interp.as_quat()
                
                pose = np.concatenate([trans_interp, quat_interp])
            
            f_out.write(f"{target_t:.6f} {pose[0]:.6f} {pose[1]:.6f} {pose[2]:.6f} "
                       f"{pose[3]:.8f} {pose[4]:.8f} {pose[5]:.8f} {pose[6]:.8f}\n")

def main():
    parser = argparse.ArgumentParser(description='转换SLAM轨迹为RoadLib odo.txt格式')
    parser.add_argument('--format', choices=['tum', 'kitti', 'euroc', 'orbslam'], 
                       required=True, help='输入格式')
    parser.add_argument('--input', required=True, help='输入轨迹文件')
    parser.add_argument('--output', required=True, help='输出odo.txt文件')
    parser.add_argument('--timestamps', help='时间戳文件（KITTI格式需要）')
    parser.add_argument('--target_timestamps', help='目标时间戳文件（用于插值）')
    
    args = parser.parse_args()
    
    if args.format == 'tum':
        convert_tum_format(args.input, args.output)
    elif args.format == 'kitti':
        if not args.timestamps:
            print("KITTI格式需要提供时间戳文件")
            return
        convert_kitti_format(args.input, args.output, args.timestamps)
    elif args.format == 'euroc':
        convert_euroc_format(args.input, args.output)
    elif args.format == 'orbslam':
        convert_orbslam_format(args.input, args.output)
    
    # 如果需要插值到特定时间戳
    if args.target_timestamps:
        temp_output = args.output + '.temp'
        os.rename(args.output, temp_output)
        interpolate_poses(temp_output, args.output, args.target_timestamps)
        os.remove(temp_output)
    
    print(f"转换完成: {args.input} -> {args.output}")

if __name__ == "__main__":
    main()
